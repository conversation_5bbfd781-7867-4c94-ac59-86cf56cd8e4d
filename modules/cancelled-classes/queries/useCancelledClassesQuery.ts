import { useQuery } from "@tanstack/react-query";
import { api } from "~/lib/api";
import { CancelledClassesResponse } from "../types";
import { useSession } from "~/modules/login/auth-provider";

export const fetchCancelledClasses = async ({
  signal,
}: {
  signal?: AbortSignal;
}): Promise<CancelledClassesResponse> => {
  try {
    const response = await api.get<CancelledClassesResponse>(
      "classes/cancelled/list",
      {
        signal,
      }
    );

    const data = await response.json();
    return data;
  } catch (err) {
    throw new Error("Could not fetch cancelled classes");
  }
};

export const useCancelledClassesQuery = () => {
  const { data: sessionData } = useSession();

  return useQuery({
    queryKey: ["cancelled-classes", sessionData?.token],
    queryFn: async ({ signal }) => fetchCancelledClasses({ signal }),
    select: (data) => data.data || [],
  });
};
