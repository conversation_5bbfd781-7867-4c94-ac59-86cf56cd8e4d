import { useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "~/lib/api";
import { showSuccessToast } from "~/components/toast";
import { CancelClassRequest, CancelClassResponse } from "../types";

export const cancelClass = async (
  data: CancelClassRequest
): Promise<CancelClassResponse> => {
  try {
    const response = await api.post<CancelClassResponse>("classes/cancel", {
      json: data,
    });

    return await response.json();
  } catch (error) {
    throw new Error("Failed to cancel class");
  }
};

export const useCancelClassMutation = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: cancelClass,
    onSuccess: async (data) => {
      if (data.success) {
        // Invalidate and refetch cancelled classes
        await queryClient.invalidateQueries({
          queryKey: ["cancelled-classes"],
        });
        
        // Invalidate classes queries to update the main classes list
        await queryClient.invalidateQueries({
          queryKey: ["classes"],
        });

        showSuccessToast(data.message || "Classes cancelled successfully");
        onSuccess?.();
      }
    },
  });
};
