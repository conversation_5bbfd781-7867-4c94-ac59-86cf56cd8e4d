import React from "react";
import { View } from "react-native";
import { router } from "expo-router";
import { Text } from "~/components/ui/text";
import {
  CancelClassForm,
  CancelClassFormValues,
} from "~/components/modules/cancelled-classes/cancel-class-form";
import { useCancelClassMutation } from "~/modules/cancelled-classes/mutations/useCancelClassMutation";
import { ScreenTracker } from "~/components/analytics/ScreenTracker";
import { useAnalytics } from "~/modules/hooks/useAnalytics";
import { formatClassDate } from "~/modules/classes/utils";

export default function CreateCancelledClassPage() {
  const { trackEvent, EVENTS } = useAnalytics();

  const { mutate: cancelClass, isPending } = useCancelClassMutation(() => {
    // Navigate back to cancelled classes list on success
    router.back();
  });

  const handleSubmit = (data: CancelClassFormValues) => {
    trackEvent(EVENTS.CLASS_ACTION, {
      action: "cancel_class_form_submit",
      classes_count: data.classes.length,
      date: formatClassDate(data.date),
    });

    // Transform form data to API format
    const cancelRequest = {
      date: formatClassDate(data.date),
      class_ids: data.classes.map((cls) => parseInt(cls.id)),
      reason: data.reason,
    };

    cancelClass(cancelRequest);
  };

  return (
    <ScreenTracker screenName="Create Cancelled Class">
      <View className="flex-1 bg-background">
        {/* Header */}
        <View className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
          <Text className="text-xl font-bold text-foreground">
            Cancel Classes
          </Text>
          <Text className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Select the date, classes, and provide a reason for cancellation
          </Text>
        </View>

        {/* Form */}
        <CancelClassForm onSubmit={handleSubmit} isLoading={isPending} />
      </View>
    </ScreenTracker>
  );
}
