import { Drawer } from "expo-router/drawer";
import { useColorScheme } from "~/lib/useColorScheme";
import { CustomDrawer } from "~/components/modules/classes/drawer-content";
import { Image } from "expo-image";
import { View } from "react-native";
import { StagingIndicator } from "~/components/StagingIndicator";
import { ThemeToggle } from "~/components/ThemeToggle";

export default function ClassesLayout() {
  const { isDarkColorScheme } = useColorScheme();

  const logSrc = isDarkColorScheme
    ? require("../../assets/images/white-logo.svg")
    : require("../../assets/images/black-logo.svg");

  return (
    <Drawer
      drawerContent={(props) => <CustomDrawer {...props} />}
      screenOptions={{
        headerTintColor: isDarkColorScheme ? "white" : "#002966",
        headerStyle: {
          height: 110,
        },
        headerTitleContainerStyle: {
          paddingTop: 20,
        },
        headerLeftContainerStyle: {
          paddingTop: 20,
        },
        headerRightContainerStyle: {
          paddingTop: 20,
        },
        drawerActiveBackgroundColor: "#069CC3",
        drawerActiveTintColor: "#fff",
        drawerInactiveTintColor: isDarkColorScheme ? "#fff" : "#333",
        drawerLabelStyle: {
          marginLeft: -20,
          fontSize: 15,
        },

        headerTitle: () => {
          return (
            <View className="flex-row items-center">
              <Image
                source={logSrc}
                style={{
                  height: 22,
                  width: 80,
                  paddingTop: 10,
                }}
              />
              <StagingIndicator />
            </View>
          );
        },
        headerRight: () => (
          <View className="mr-6">
            <ThemeToggle />
          </View>
        ),
      }}
    >
      <Drawer.Screen
        name="(tabs)"
        options={{
          drawerItemStyle: { display: "none" },
          title: "Upace",
        }}
      />
      <Drawer.Screen
        name="[id]"
        options={{
          drawerItemStyle: { display: "none" },
          headerShown: false,
        }}
      />
      <Drawer.Screen
        name="(appointment)"
        options={{
          drawerItemStyle: { display: "none" },
          headerShown: false,
        }}
      />
      <Drawer.Screen
        name="(subs)"
        options={{
          headerShown: false,
          drawerItemStyle: { display: "none" },
        }}
      />
      <Drawer.Screen
        name="terms"
        options={{
          drawerItemStyle: { display: "none" },
        }}
      />
      <Drawer.Screen
        name="cancelled-classes"
        options={{
          drawerItemStyle: { display: "none" },
          headerShown: false,
        }}
      />
    </Drawer>
  );
}
