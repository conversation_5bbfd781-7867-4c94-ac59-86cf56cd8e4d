import {
  Drawer<PERSON>ontent<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  DrawerItemList,
} from "@react-navigation/drawer";

import { useSession } from "~/modules/login/auth-provider";

import { View, Alert, Pressable } from "react-native";
import AntDesign from "@expo/vector-icons/AntDesign";
// import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import Ionicons from "@expo/vector-icons/Ionicons";
import { Text } from "~/components/ui/text";
import { useColorScheme } from "~/lib/useColorScheme";
import { getInitials } from "~/modules/classes/utils";
import { BaseAvatar } from "../classes/avatar";
import { useRouter } from "expo-router";

import appConfig from "../../../app.json";

export const CustomDrawer = (props: any) => {
  const { signOut, data } = useSession();
  const { isDarkColorScheme } = useColorScheme();
  const router = useRouter();

  // App version from app.json
  const appVersion = appConfig?.expo?.version;

  const handleSignOut = () => {
    Alert.alert("Sign out", "Are you sure you want to sign out?", [
      {
        text: "Cancel",
        style: "cancel",
      },
      { text: "OK", onPress: signOut },
    ]);
  };

  const openTermsAndConditions = () => {
    // Close the drawer first
    props.navigation.closeDrawer();
    // Navigate to the Terms screen
    router.push("/(classes)/terms");
  };

  const openCancelledClasses = () => {
    // Close the drawer first
    props.navigation.closeDrawer();
    // Navigate to the Cancelled Classes screen
    router.push("/(classes)/cancelled-classes");
  };

  // const openChangePassword = () => {
  //   // This would typically navigate to a change password screen
  //   // For now, we'll just show an alert
  //   Alert.alert("Change Password", "This feature will be available soon.", [
  //     { text: "OK" },
  //   ]);
  // };

  const userName = data ? `${data.first_name} ${data.last_name}` : "User";
  const userEmail = data?.email || "";

  return (
    <View style={{ flex: 1 }} className="bg-background dark:bg-background">
      <View className="pt-16 pb-6 px-5 bg-[#1A237E]">
        <View className="flex-row items-center">
          <BaseAvatar url={""} name={getInitials(userName)} size={50} />
          <View className="ml-3 flex-1">
            <Text
              className="font-bold text-base text-white"
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {userName}
            </Text>
            <Text
              className="text-white text-xs opacity-80"
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {userEmail}
            </Text>
          </View>
        </View>
      </View>

      {/* Navigation Items */}
      <DrawerContentScrollView
        {...props}
        scrollEnabled={true}
        contentContainerStyle={{
          paddingTop: 0, // Remove default padding to avoid extra space
        }}
      >
        <DrawerItemList {...props} />

        {/* Cancelled Classes */}
        <Pressable
          className="flex-row items-center px-5 py-3"
          android_ripple={{ color: "rgba(0, 0, 0, 0.1)" }}
          onPress={openCancelledClasses}
        >
          <Ionicons
            name="close-circle-outline"
            size={15}
            color={isDarkColorScheme ? "white" : "black"}
            style={{ width: 24 }}
          />
          <Text className="ml-3 dark:text-white">Cancelled Classes</Text>
        </Pressable>

        {/* Account Section */}
        <View className="mt-4">
          <Text className="text-gray-500 dark:text-gray-400 px-5 mb-2 text-xs uppercase">
            Account
          </Text>

          {/* Terms & Conditions */}
          <Pressable
            className="flex-row items-center px-5 py-3"
            android_ripple={{ color: "rgba(0, 0, 0, 0.1)" }}
            onPress={openTermsAndConditions}
          >
            <Ionicons
              name="document-text-outline"
              size={15}
              color={isDarkColorScheme ? "white" : "black"}
              style={{ width: 24 }}
            />
            <Text className="ml-3 dark:text-white">Terms & Conditions</Text>
          </Pressable>

          {/* Change Password */}
          {/* <Pressable
            className="flex-row items-center px-5 py-3"
            android_ripple={{ color: "rgba(0, 0, 0, 0.1)" }}
            onPress={openChangePassword}
            disabled
          >
            <MaterialIcons
              name="lock-outline"
              size={22}
              color={isDarkColorScheme ? "white" : "black"}
              style={{ width: 24 }}
            />
            <Text className="ml-3 dark:text-white">Change Password</Text>
          </Pressable> */}

          {/* Logout */}
          <Pressable
            className="flex-row items-center px-5 py-3"
            android_ripple={{ color: "rgba(0, 0, 0, 0.1)" }}
            onPress={handleSignOut}
          >
            <AntDesign
              name="logout"
              size={15}
              color={isDarkColorScheme ? "white" : "black"}
              style={{ width: 24 }}
            />
            <Text className="ml-3 dark:text-white">Logout</Text>
          </Pressable>
        </View>
      </DrawerContentScrollView>

      {/* App Version */}
      <View className="border-t-gray-200 border-t-2 p-5 pb-5 items-center">
        <Text className="text-gray-500 dark:text-gray-400 text-xs">
          Version {appVersion}
        </Text>
      </View>
    </View>
  );
};
